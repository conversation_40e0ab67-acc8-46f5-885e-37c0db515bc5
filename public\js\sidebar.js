// Mobile Sidebar Functionality - Arabic RTL

class MobileSidebar {
  constructor() {
    this.sidebar = document.getElementById('mobileSidebar');
    this.toggleBtn = document.getElementById('mobileMenuToggle');
    this.closeBtn = document.getElementById('sidebarClose');
    this.overlay = document.getElementById('sidebarOverlay');
    this.body = document.body;
    
    this.isOpen = false;
    
    this.init();
  }

  init() {
    if (!this.sidebar || !this.toggleBtn) return;
    
    // Bind events
    this.toggleBtn.addEventListener('click', () => this.toggle());
    
    if (this.closeBtn) {
      this.closeBtn.addEventListener('click', () => this.close());
    }
    
    if (this.overlay) {
      this.overlay.addEventListener('click', () => this.close());
    }
    
    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768 && this.isOpen) {
        this.close();
      }
    });
    
    // Sync user data between navbar and sidebar
    this.syncUserData();
    
    // Handle logout buttons
    this.handleLogoutButtons();
    
    // Handle dashboard links
    this.handleDashboardLinks();
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    if (this.isOpen) return;

    this.isOpen = true;
    this.sidebar.classList.add('active');
    this.toggleBtn.classList.add('active');
    this.body.classList.add('sidebar-open');

    // Add animation delay for menu items
    const menuItems = this.sidebar.querySelectorAll('.sidebar-link');
    menuItems.forEach((item, index) => {
      item.style.animationDelay = `${(index + 1) * 0.1}s`;
    });

    // Sync user data when opening
    this.syncUserData();

    // Focus management for accessibility
    setTimeout(() => {
      const firstLink = this.sidebar.querySelector('.sidebar-link');
      if (firstLink) {
        firstLink.focus();
      }
    }, 300);
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.sidebar.classList.remove('active');
    this.toggleBtn.classList.remove('active');
    this.body.classList.remove('sidebar-open');

    // Return focus to toggle button for accessibility
    this.toggleBtn.focus();
  }

  syncUserData() {
    // Sync profile images
    const navProfileImage = document.getElementById('navProfileImage');
    const sidebarProfileImage = document.getElementById('sidebarProfileImage');
    
    if (navProfileImage && sidebarProfileImage) {
      sidebarProfileImage.src = navProfileImage.src;
    }
    
    // Sync user names
    const navUserName = document.getElementById('navUserName');
    const sidebarUserName = document.getElementById('sidebarUserName');
    
    if (navUserName && sidebarUserName) {
      sidebarUserName.textContent = navUserName.textContent;
    }
    
    // Sync user roles
    const navUserRole = document.getElementById('navUserRole');
    const sidebarUserRole = document.getElementById('sidebarUserRole');
    
    if (navUserRole && sidebarUserRole) {
      sidebarUserRole.textContent = navUserRole.textContent;
    }
    
    // Sync dashboard text and links
    const dashboardText = document.getElementById('dashboardText');
    const sidebarDashboardText = document.getElementById('sidebarDashboardText');
    
    if (dashboardText && sidebarDashboardText) {
      sidebarDashboardText.textContent = dashboardText.textContent;
    }
    
    const dashboardLink = document.getElementById('dashboardLink');
    const sidebarDashboardLink = document.getElementById('sidebarDashboardLink');
    
    if (dashboardLink && sidebarDashboardLink) {
      sidebarDashboardLink.href = dashboardLink.href;
    }
  }

  handleLogoutButtons() {
    const navLogoutBtn = document.getElementById('logoutBtn');
    const sidebarLogoutBtn = document.getElementById('sidebarLogoutBtn');
    
    if (sidebarLogoutBtn && navLogoutBtn) {
      sidebarLogoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.close();
        // Trigger the main logout functionality
        navLogoutBtn.click();
      });
    }
  }

  handleDashboardLinks() {
    const navDashboardLink = document.getElementById('dashboardLink');
    const sidebarDashboardLink = document.getElementById('sidebarDashboardLink');
    
    if (sidebarDashboardLink && navDashboardLink) {
      sidebarDashboardLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.close();
        // Navigate to dashboard
        if (navDashboardLink.href && navDashboardLink.href !== '#') {
          window.location.href = navDashboardLink.href;
        } else {
          navDashboardLink.click();
        }
      });
    }
  }

  // Method to update user data from external sources
  updateUserData(userData) {
    if (userData.profileImage) {
      const sidebarProfileImage = document.getElementById('sidebarProfileImage');
      if (sidebarProfileImage) {
        sidebarProfileImage.src = userData.profileImage;
      }
    }
    
    if (userData.name) {
      const sidebarUserName = document.getElementById('sidebarUserName');
      if (sidebarUserName) {
        sidebarUserName.textContent = userData.name;
      }
    }
    
    if (userData.role) {
      const sidebarUserRole = document.getElementById('sidebarUserRole');
      if (sidebarUserRole) {
        sidebarUserRole.textContent = userData.role;
      }
    }
  }

  // Method to set active menu item
  setActiveMenuItem(href) {
    const menuItems = this.sidebar.querySelectorAll('.sidebar-link');
    menuItems.forEach(item => {
      item.classList.remove('active');
      if (item.getAttribute('href') === href || 
          (href.includes('profile') && item.getAttribute('href') === '/profile.html')) {
        item.classList.add('active');
      }
    });
  }
}

// Initialize sidebar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.mobileSidebar = new MobileSidebar();
  
  // Set active menu item based on current page
  const currentPath = window.location.pathname;
  if (window.mobileSidebar) {
    window.mobileSidebar.setActiveMenuItem(currentPath);
  }
});

// Utility functions for sidebar
window.sidebarUtils = {
  // Update user data in sidebar
  updateUserData: function(userData) {
    if (window.mobileSidebar) {
      window.mobileSidebar.updateUserData(userData);
    }
  },

  // Set active menu item
  setActiveMenuItem: function(href) {
    if (window.mobileSidebar) {
      window.mobileSidebar.setActiveMenuItem(href);
    }
  },

  // Close sidebar programmatically
  closeSidebar: function() {
    if (window.mobileSidebar) {
      window.mobileSidebar.close();
    }
  },

  // Open sidebar programmatically
  openSidebar: function() {
    if (window.mobileSidebar) {
      window.mobileSidebar.open();
    }
  }
};

// Handle touch gestures for mobile
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', (e) => {
  touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', (e) => {
  touchEndX = e.changedTouches[0].screenX;
  handleSwipeGesture();
});

function handleSwipeGesture() {
  const swipeThreshold = 50;
  const swipeDistance = touchEndX - touchStartX;

  // Swipe from left edge to open sidebar (RTL)
  if (touchStartX < 50 && swipeDistance > swipeThreshold && window.mobileSidebar && !window.mobileSidebar.isOpen) {
    window.mobileSidebar.open();
  }

  // Swipe right to close sidebar (RTL)
  if (swipeDistance > swipeThreshold && window.mobileSidebar && window.mobileSidebar.isOpen) {
    window.mobileSidebar.close();
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MobileSidebar;
}
