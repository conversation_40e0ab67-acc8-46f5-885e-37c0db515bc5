/* Admin Dashboard CSS - Arabic RTL */

:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  
  /* Grays */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Layout */
  --sidebar-width: 280px;
  --navbar-height: 70px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Amiri', Arial, Tahoma, sans-serif;
  background-color: var(--gray-50);
  color: var(--gray-900);
  line-height: 1.6;
  direction: rtl;
  overflow-x: hidden;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--navbar-height);
  background: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 2rem;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.navbar-brand:hover {
  color: var(--primary-dark);
}

.navbar-brand i {
  font-size: 1.5rem;
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.nav-profile-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--gray-200);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-details span {
  font-weight: 600;
  color: var(--gray-900);
}

.user-details small {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}

.nav-link:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--danger-color);
}

.logout-btn:hover {
  background-color: #fef2f2;
  color: var(--danger-color);
}

/* Admin Container */
.admin-container {
  display: flex;
  margin-top: var(--navbar-height);
  min-height: calc(100vh - var(--navbar-height));
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background: white;
  border-left: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: var(--navbar-height);
  right: 0;
  height: calc(100vh - var(--navbar-height));
  overflow-y: auto;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
}

.sidebar-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-900);
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--gray-600);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0;
  border-right: 3px solid transparent;
}

.sidebar .nav-link:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
  border-right-color: var(--primary-light);
}

.sidebar .nav-link.active {
  background-color: #eff6ff;
  color: var(--primary-color);
  border-right-color: var(--primary-color);
  font-weight: 600;
}

.sidebar .nav-link i {
  width: 20px;
  text-align: center;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-right: var(--sidebar-width);
  padding: 2rem;
  background-color: var(--gray-50);
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.section-header p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-card:nth-child(1) .stat-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-card:nth-child(2) .stat-icon {
  background: linear-gradient(135deg, var(--success-color), #34d399);
}

.stat-card:nth-child(3) .stat-icon {
  background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.stat-card:nth-child(4) .stat-icon {
  background: linear-gradient(135deg, var(--danger-color), #f87171);
}

.stat-content h3 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.stat-content p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* Search and Filters */
.search-box {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  background: white;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.search-box i {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  background: white;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  cursor: pointer;
  transition: border-color 0.3s ease;
}

select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Table Styles */
.table-container {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid var(--gray-200);
}

.users-table th {
  background-color: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.users-table tbody tr:hover {
  background-color: var(--gray-50);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-inactive {
  background-color: #fef3c7;
  color: #92400e;
}

.status-locked {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.btn-secondary:hover {
  background-color: var(--gray-300);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-xs);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 0;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 500px;
  box-shadow: var(--shadow-lg);
  animation: modalSlideIn 0.3s ease;
}

.modal-content.modal-large {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.close {
  color: var(--gray-400);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close:hover {
  color: var(--gray-600);
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  margin-top: 0.25rem;
  display: block;
}

.form-group input.error,
.form-group select.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.form-group input.success,
.form-group select.success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgb(16 185 129 / 0.1);
}

/* Course Detail Modal */
.course-detail-header {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
}

.course-detail-image {
  width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.course-detail-info {
  flex: 1;
}

.course-detail-info h2 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.course-instructor {
  color: var(--gray-600);
  margin-bottom: 1rem;
}

.course-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-info {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge-secondary {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.course-detail-body h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.course-detail-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

.meta-item {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.meta-item strong {
  color: var(--gray-900);
}

.course-actions-detail {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--gray-200);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Loading Spinner */
.loading-spinner {
  display: none;
  position: fixed;
  z-index: 3000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.pagination button {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  background: white;
  color: var(--gray-700);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination button:hover:not(:disabled) {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.pagination button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Courses Grid */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.course-card {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.course-thumbnail {
  width: 100%;
  height: 180px;
  object-fit: cover;
  background: var(--gray-100);
}

.course-content {
  padding: 1.5rem;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.course-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.course-status {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.status-published {
  background-color: #dcfce7;
  color: #166534;
}

.status-draft {
  background-color: #fef3c7;
  color: #92400e;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.course-meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.course-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

.course-price {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
}

.course-price.free {
  color: var(--success-color);
}

.course-actions {
  display: flex;
  gap: 0.5rem;
}

.course-actions .btn {
  padding: 0.5rem;
  font-size: var(--font-size-sm);
  min-width: auto;
}

/* Recent Activity */
.recent-activity {
  background: white;
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.activity-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.activity-actions {
  display: flex;
  gap: 0.5rem;
}

.activity-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--gray-500);
}

.activity-loading .spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--gray-50);
  border-radius: var(--border-radius);
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.activity-item:hover {
  background-color: var(--gray-100);
  border-left-color: var(--primary-color);
  transform: translateX(-2px);
}

.activity-item:last-child {
  margin-bottom: 0;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-content p {
  margin-bottom: 0.25rem;
  color: var(--gray-700);
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-content small {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activity-content small::before {
  content: '•';
  color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-right: 0;
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .section-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
  
  .nav-user {
    display: none;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .users-table {
    min-width: 600px;
  }
}

/* Course Videos Section */
.course-videos-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--gray-200);
}

.course-videos-section h3 {
  color: var(--gray-900);
  margin-bottom: 1.5rem;
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.course-videos-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-video-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.admin-video-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.video-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.video-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  gap: 1rem;
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  flex-wrap: wrap;
}

.video-meta span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.video-status.published {
  color: var(--success-color);
}

.video-status.draft {
  color: var(--warning-color);
}

.video-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.loading-videos,
.no-videos,
.error-loading {
  text-align: center;
  padding: 2rem;
  color: var(--gray-500);
}

.loading-videos i,
.no-videos i,
.error-loading i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--gray-400);
}

.no-videos {
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: 2px dashed var(--gray-300);
}

.error-loading {
  background: #fef2f2;
  border-radius: var(--border-radius);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
}

/* Video Modal */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 2rem;
}

.video-modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  max-width: 1000px;
  box-shadow: var(--shadow-lg);
}

.video-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.video-modal-header h3 {
  color: var(--gray-900);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
}

.close-video {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-video:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.video-modal-body {
  padding: 0;
}

.video-modal-body video {
  width: 100%;
  height: auto;
  max-height: 70vh;
  display: block;
}

/* Responsive Design for Videos */
@media (max-width: 768px) {
  .admin-video-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .video-thumbnail {
    width: 100%;
    height: 120px;
  }

  .video-actions {
    justify-content: center;
    gap: 0.75rem;
  }

  .video-modal {
    padding: 1rem;
  }

  .video-modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .video-modal-header {
    padding: 0.75rem 1rem;
  }

  .video-modal-header h3 {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .course-videos-section h3 {
    font-size: 1rem;
  }

  .admin-video-item {
    padding: 0.75rem;
  }

  .video-title {
    font-size: 0.9rem;
  }

  .video-description {
    font-size: 0.8rem;
  }

  .video-meta {
    font-size: 0.7rem;
    gap: 0.5rem;
  }

  .video-actions .btn {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}
 