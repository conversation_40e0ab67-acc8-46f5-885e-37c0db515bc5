// ===============================
// Local Storage Helpers
// ===============================
function setAuthData(token, user) {
  localStorage.setItem('token', token);
  localStorage.setItem('user', JSON.stringify(user));

}

function getToken() {
  return localStorage.getItem('token');
}

function getCurrentUser() {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
}

function clearAuthData() {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
 }

function isLoggedIn() {
  return !!getToken();
}

function getAuthHeaders() {
  const token = getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
}

// ===============================
// Route Protection
// ===============================
function requireAuth() {
  if (!isLoggedIn()) {
    window.location.href = '/login.html';
    return false;
  }
  return true;
}

function requireGuest() {
  if (isLoggedIn()) {
    window.location.href = '/';
    return false;
  }
  return true;
}

function requireAdmin() {
  const user = getCurrentUser();
  if (!isLoggedIn() || !user || user.role !== 'admin') {
    window.location.href = '/';
    return false;
  }
  return true;
}

// ===============================
// Login
// ===============================
document.addEventListener('DOMContentLoaded', () => {
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitBtn = loginForm.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.disabled = true;
      submitBtn.textContent = 'Logging in...';

      try {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (data.success) {
          setAuthData(data.token, data.data);
          showToast('success', 'Login successful!');
        
          if (data.data.role === 'admin') {
            console.log("redirecting to dashboard");
            window.location.href = '/admin-dashboard.html';
          } else {
            console.log("redirecting to home");
            window.location.href = '/';
          }
        }
        
        else {
          showToast('error', data.message || 'Login failed');
        }
      } catch (err) {
        console.error('Login error:', err);
        showToast('error', 'Error during login');
      } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
      }
    });
  }
});

// ===============================
// Register
// ===============================
document.addEventListener('DOMContentLoaded', () => {
  const registerForm = document.getElementById('registerForm');
  if (registerForm) {
    registerForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitBtn = registerForm.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.disabled = true;
      submitBtn.textContent = 'Registering...';

      try {
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name, email, password }),
        });

        const data = await response.json();

        if (data.success) {
          setAuthData(data.token, data.data);
          if (data.data.role === 'admin') {
            window.location.href = '/admin-dashboard.html';
          } else {
            window.location.href = '/';
          }
          
        } else {
          showToast('error', data.message || 'Register failed');
        }
      } catch (err) {
        console.error('Register error:', err);
        showToast('error', 'Error during register');
      } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
      }
    });
  }
});

// ===============================
// Logout
// ===============================
async function logout() {
  try {
    await fetch('/api/auth/logout', {
      method: 'POST',
      headers: getAuthHeaders(),
    });
  } catch (err) {
    console.error('Logout error:', err);
  } finally {
    clearAuthData();


    window.location.href = '/';
  }
}

// ===============================
// Navbar Handling
// ===============================
document.addEventListener('DOMContentLoaded', () => {
  const navbarContent = document.getElementById('navbarContent');
  if (navbarContent) {
    const user = getCurrentUser();

    if (user) {
      let navbarHTML = `
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
            ${user.name}
          </a>
          <ul class="dropdown-menu">
      `;

      if (user.role === 'admin') {
        navbarHTML += `
          <li><a class="dropdown-item" href="/admin-dashboard.html">Dashboard</a></li>
          <li><a class="dropdown-item" href="/admin-dashboard.html">Admin Dashboard</a></li>
          <li><a class="dropdown-item" href="/add-course.html">Add Course</a></li>
        `;
      } else {
        navbarHTML += `
          <li><a class="dropdown-item" href="/admin-dashboard.html">Dashboard</a></li>
        `;
      }

      navbarHTML += `
            <li><a class="dropdown-item" href="/profile.html">Profile</a></li>
            <li><a class="dropdown-item" href="#" onclick="logout()">Logout</a></li>
          </ul>
        </li>
      `;

      navbarContent.innerHTML = navbarHTML;
    } else {
      navbarContent.innerHTML = `
        <li class="nav-item"><a class="nav-link" href="/register.html">Register</a></li>
        <li class="nav-item"><a class="nav-link" href="/login.html">Login</a></li>
      `;
    }
  }
});
