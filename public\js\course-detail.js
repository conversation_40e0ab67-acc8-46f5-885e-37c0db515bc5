  // Enhanced Course Detail JavaScript with API Integration
    let currentCourse = null;
    let currentUser = null;

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      initializeAuth();
      updateNavbar();
      initializeTabs();
      loadCourseDetail();
    });

    // Initialize authentication
    function initializeAuth() {
      const token = localStorage.getItem('token');
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      currentUser = user;
    }

    // Update navbar based on authentication status
    function updateNavbar() {
      const navAuth = document.getElementById('navAuth');
      const token = localStorage.getItem('token');
      const user = JSON.parse(localStorage.getItem('user') || '{}');

      if (token && user.name) {
        navAuth.innerHTML = `
          <div class="user-menu">
            <span class="user-name">مرحباً، ${user.name}</span> 
            <button onclick="logout()" class="btn btn-outline">تسجيل الخروج</button>
          </div>
        `;
      } else {
        navAuth.innerHTML = `
          <a href="/login.html" class="btn btn-secondary">تسجيل الدخول</a>
          <a href="/register.html" class="btn btn-primary">ابدأ الآن</a>
        `;
      }
    }

    // Logout function
    function logout() {
      if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
      }
    }

    // Initialize tabs
    function initializeTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabPanes = document.querySelectorAll('.tab-pane');
      
      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');
          
          // Remove active class from all tabs and panes
          tabBtns.forEach(b => b.classList.remove('active'));
          tabPanes.forEach(p => p.classList.remove('active'));
          
          // Add active class to clicked tab and corresponding pane
          this.classList.add('active');
          document.getElementById(targetTab).classList.add('active');
        });
      });
    }

    // Load course detail from API
    async function loadCourseDetail() {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const courseId = urlParams.get('id');
        
        if (!courseId) {
          window.location.href = '/courses.html';
          return;
        }
        
        showLoading();
        
        // Use the provided API endpoint
        const response = await fetch(`http://localhost:4009/api/courses/${courseId}`);
        
        if (response.ok) {
          const responseData = await response.json();
          currentCourse = responseData.data;
          
          displayCourseHeader(currentCourse);
          displayCourseOverview(currentCourse);
          displayCourseCurriculum(currentCourse);
          displayCourseInstructor(currentCourse);
          displayCourseSidebar(currentCourse);

          // Load related courses
          loadRelatedCourses(currentCourse.category, courseId);
          
          // Update page title and breadcrumb
          document.title = `${currentCourse.title} - منصة دوراتي`;
          document.getElementById('courseBreadcrumb').textContent = currentCourse.title;
          
        } else {
          throw new Error('Course not found');
        }
      } catch (error) {
        console.error('Error loading course:', error);
        showError('حدث خطأ في تحميل تفاصيل الدورة');
      } finally {
        hideLoading();
      }
    }

    // Display course header
    function displayCourseHeader(course) {
      const headerContent = document.getElementById('courseHeaderContent');
      
      const categoryNames = {
        development: 'البرمجة والتطوير',
        business: 'الأعمال والإدارة',
        design: 'التصميم والجرافيك',
        marketing: 'التسويق الرقمي',
        languages: 'اللغات',
        other: 'أخرى'
      };
      
      const levelNames = {
        beginner: 'مبتدئ',
        intermediate: 'متوسط',
        advanced: 'متقدم'
      };
      
      const rating = course.averageRating > 0 ? course.averageRating.toFixed(1) : 'جديد';
      const enrollmentCount = course.enrollmentCount || 0;
      
      headerContent.innerHTML = `
        <div class="course-header-info">
          <div class="course-category">${categoryNames[course.category] || course.category}</div>
          <h1 class="course-title">${course.title}</h1>
          <p class="course-subtitle">${course.description}</p>
          
          <div class="course-meta">
            <div class="meta-item">
              <i class="fas fa-star"></i>
              <span>${rating}</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-users"></i>
              <span>${enrollmentCount} طالب</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-signal"></i>
              <span>${levelNames[course.level] || course.level}</span>
            </div>
            
            <div class="meta-item">
              <i class="fas fa-clock"></i>
              <span>آخر تحديث: ${new Date(course.updatedAt).toLocaleDateString('ar-SA')}</span>
            </div>
          </div>
          
          <div class="course-instructor-info">
            <i class="fas fa-user"></i>
            <span>المدرب: ${course.instructor}</span>
          </div>
        </div>
      `;
    }

    // Display course overview
    function displayCourseOverview(course) {
      const descriptionElement = document.getElementById('courseDescription');
      const featuresElement = document.getElementById('courseFeatures');
      
      descriptionElement.innerHTML = `
        <h3>وصف الدورة</h3>
        <p>${course.description}</p>
        
        ${course.tags && course.tags.length > 0 ? `
          <div class="course-tags">
            <h4>الكلمات المفتاحية:</h4>
            <div class="tags-list">
              ${course.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
          </div>
        ` : ''}
      `;
      
      // Sample features - in a real app, these would come from the course data
      const features = [
        'تعلم المفاهيم الأساسية والمتقدمة',
        'مشاريع عملية وتطبيقية',
        'شهادة إتمام معتمدة',
        'دعم فني مستمر',
        'وصول مدى الحياة للمحتوى',
        'تحديثات  للمحتوى'
      ];
      
      featuresElement.innerHTML = features.map(feature => `
        <div class="feature-item">
          <i class="fas fa-check"></i>
          <span>${feature}</span>
        </div>
      `).join('');
    }

    // Display course curriculum using the videos from API response
    function displayCourseCurriculum(course) {
      const statsElement = document.getElementById('curriculumStats');
      const listElement = document.getElementById('curriculumList');

      const videos = course.videos || [];
      const videoCount = videos.length;
      const totalDuration = videos.reduce((total, video) => total + (video.duration || 0), 0);

      const hours = Math.floor(totalDuration / 3600);
      const minutes = Math.floor((totalDuration % 3600) / 60);

      statsElement.innerHTML = `
        <div class="stat">
          <span class="stat-number">${videoCount}</span>
          <span class="stat-label">فيديو</span>
        </div>
        <div class="stat">
          <span class="stat-number">${hours}س ${minutes}د</span>
          <span class="stat-label">المدة الإجمالية</span>
        </div>
      `;

      if (videos.length > 0) {
        listElement.innerHTML = videos
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .map((video, videoIndex) => {
            const videoDuration = video.duration ?
              `${Math.floor(video.duration / 60)}:${(video.duration % 60).toString().padStart(2, '0')}` :
              '--:--';

            return `
              <div class="curriculum-item" onclick="playVideo('${video._id}', '${video.videoUrl}', '${video.title.replace(/'/g, "\\'")}')">
                <div class="curriculum-item-header">
                  <div class="item-info">
                    <div class="video-thumbnail">
                      <div class="video-thumb-placeholder">
                        <i class="fas fa-play-circle"></i>
                      </div>
                    </div>
                    <div class="video-details">
                      <span class="item-title">${video.title}</span>
                      <span class="video-number">الفيديو ${videoIndex + 1}</span>
                    </div>
                  </div>
                  <div class="item-duration">
                    <i class="fas fa-clock"></i>
                    ${videoDuration}
                  </div>
                </div>
                ${video.description ? `
                  <div class="item-description">${video.description}</div>
                ` : ''}
              </div>
            `;
          }).join('');
      } else {
        listElement.innerHTML = `
          <div class="no-content">
            <i class="fas fa-video"></i>
            <p>لم يتم إضافة فيديوهات لهذه الدورة بعد</p>
          </div>
        `;
      }
    }

    // Play video function with enhanced modal
    function playVideo(videoId, videoUrl, videoTitle) {
      // Create video modal
      const modal = document.createElement('div');
      modal.className = 'video-modal';
      modal.innerHTML = `
        <div class="video-modal-content">
          <div class="video-modal-header">
            <h3>${videoTitle}</h3>
            <button class="close-video" onclick="closeVideoModal()">&times;</button>
          </div>
          <div class="video-modal-body">
            <video controls autoplay width="100%" style="max-height: 70vh;">
              <source src="${videoUrl}" type="video/mp4">
              متصفحك لا يدعم تشغيل الفيديو
            </video>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      document.body.style.overflow = 'hidden';
      
      // Close modal on background click
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeVideoModal();
        }
      });
      
      // Close modal on escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          closeVideoModal();
        }
      });
    }

    // Close video modal
    function closeVideoModal() {
      const modal = document.querySelector('.video-modal');
      if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
      }
    }

    // Display course instructor
    function displayCourseInstructor(course) {
      const instructorElement = document.getElementById('instructorInfo');
      
      const categoryInfo = {
        development: 'البرمجة والتطوير',
        business: 'الأعمال والإدارة',
        design: 'التصميم والجرافيك',
        marketing: 'التسويق الرقمي',
        languages: 'اللغات',
        other: 'عام'
      };
      
      instructorElement.innerHTML = `
        <div class="instructor-card">
          <div class="instructor-avatar">
            <i class="fas fa-user-circle"></i>
          </div>
          <div class="instructor-details">
            <h3>${course.instructor}</h3>
            <p class="instructor-title">مدرب معتمد في ${categoryInfo[course.category] || 'التعليم العام'}</p>
            <p class="instructor-bio">
              مدرب متخصص في مجال ${categoryInfo[course.category] || course.category} مع سنوات من الخبرة في التدريس والتطوير.
              يهدف إلى تقديم محتوى تعليمي عالي الجودة يساعد الطلاب على تحقيق أهدافهم المهنية والأكاديمية.
              يتميز بأسلوب تعليمي تفاعلي ومبسط يناسب جميع المستويات.
            </p>
          </div>
        </div>
      `;
    }

    // Display course sidebar
    function displayCourseSidebar(course) {
      const previewElement = document.getElementById('coursePreview');
      const priceElement = document.getElementById('coursePrice');
      const actionsElement = document.getElementById('courseActions');
      const includesElement = document.getElementById('courseIncludes');
      
      // Course preview
      previewElement.innerHTML = `
        <div class="preview-image">
          <img src="${course.thumbnail || '/uploads/no-photo.jpg'}" alt="${course.title}" onerror="this.src='/uploads/no-photo.jpg'">
          <div class="preview-play" onclick="previewCourse()">
            <i class="fas fa-play"></i>
          </div>
        </div>
      `;
      
      // Course price
      const price = course.price > 0 ? `${course.price} ج` : 'مجاني';
      priceElement.innerHTML = `
        <div class="price-info">
          <span class="current-price">${price}</span>
          ${course.price > 0 ? ' ' : '<span class="price-note">دورة مجانية بالكامل</span>'}
        </div>
      `;
      
      // Course actions
      const isLoggedIn = localStorage.getItem('token');
      actionsElement.innerHTML = `
        ${isLoggedIn ? `
        ` : `
          <button class="btn btn-primary btn-large" onclick="redirectToLogin()">
            <i class="fas fa-sign-in-alt"></i>
            سجل دخول للتسجيل
          </button>
        `}
        
        <button class="btn btn-outline btn-large" onclick="addToWishlist('${course._id}')">
          <i class="fas fa-heart"></i>
          إضافة للمفضلة
        </button>
      `;
      
      // Course includes
      const videos = course.videos || [];
      const totalDuration = videos.reduce((total, video) => total + (video.duration || 0), 0);
      const hours = Math.floor(totalDuration / 3600);
      const minutes = Math.floor((totalDuration % 3600) / 60);
      
      const includes = [
        `${videos.length} فيديو تعليمي`,
        `${hours} ساعة و ${minutes} دقيقة من المحتوى`,
        'وصول مدى الحياة',
        'شهادة إتمام معتمدة',
        'دعم فني مستمر',
        'تحديثات للمحتوي'
      ];
      
      includesElement.innerHTML = includes.map(item => `
        <li><i class="fas fa-check"></i> ${item}</li>
      `).join('');
    }

    // Preview course function
    function previewCourse() {
      const videos = currentCourse.videos || [];
      if (videos.length > 0) {
        const firstVideo = videos.sort((a, b) => (a.order || 0) - (b.order || 0))[0];
        playVideo(firstVideo._id, firstVideo.videoUrl, firstVideo.title);
      } else {
        alert('لا توجد فيديوهات معاينة متاحة لهذه الدورة');
      }
    }

    // Load related courses
    async function loadRelatedCourses(category, excludeId) {
      try {
        const response = await fetch(`http://localhost:4009/api/courses?category=${category}&limit=4&isPublished=true`);
        
        if (response.ok) {
          const data = await response.json();
          const courses = data.data || data.courses || [];
          const relatedCourses = courses.filter(course => course._id !== excludeId);
          
          displayRelatedCourses(relatedCourses.slice(0, 3));
        }
      } catch (error) {
        console.error('Error loading related courses:', error);
        // Display empty state
        displayRelatedCourses([]);
      }
    }
function displayRelatedCourses(courses) {
  const relatedElement = document.getElementById('relatedCourses');
  
  if (courses.length === 0) {
    relatedElement.innerHTML = `
      <div class="no-content">
        <i class="fas fa-graduation-cap"></i>
        <p>لا توجد دورات ذات صلة في الوقت الحالي</p>
      </div>
    `;
    return;
  }
  
  const categoryNames = {
    development: 'البرمجة والتطوير',
    business: 'الأعمال والإدارة',
    design: 'التصميم والجرافيك',
    marketing: 'التسويق الرقمي',
    languages: 'اللغات',
    other: 'أخرى'
  };
  
  relatedElement.innerHTML = courses.slice(0, 3).map(course => {
    const price = course.price > 0 ? `${course.price} ج` : 'مجاني';
    const rating = course.averageRating > 0 ? course.averageRating.toFixed(1) : 'جديد';
    
    return `
      <div class="course-card" onclick="viewCourse('${course._id}')">
        <div class="course-image">
          <img src="${course.thumbnail || '/uploads/no-photo.jpg'}" alt="${course.title}" onerror="this.src='/uploads/no-photo.jpg'">
          <div class="course-price">${price}</div>
        </div>
        <div class="course-content">
          <div class="category" style="margin-bottom: 0.5rem; font-size: 0.875rem; color: var(--primary-color); font-weight: 600;">
            ${categoryNames[course.category] || course.category}
          </div>
          <h3>${course.title}</h3>
          <p>${course.instructor}</p>
          <div class="rating">
            <i class="fas fa-star"></i>
            <span>${rating}</span>
          </div>
        </div>
      </div>
    `;
  }).join('');
}


    // View course
    function viewCourse(courseId) {
      window.location.href = `?id=${courseId}`;
    }

    // Enroll in course
    async function enrollInCourse(courseId) {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          redirectToLogin();
          return;
        }
        
        const response = await fetch('http://localhost:4009/api/enrollments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ course: courseId })
        });
        
        if (response.ok) {
          alert('تم التسجيل في الدورة بنجاح! 🎉');
          // Update enrollment count
          if (currentCourse) {
            currentCourse.enrollmentCount = (currentCourse.enrollmentCount || 0) + 1;
            displayCourseHeader(currentCourse);
          }
          // Redirect to course learning page or dashboard
          window.location.href = '/dashboard.html';
        } else {
          const errorData = await response.json();
          alert(errorData.message || 'حدث خطأ في التسجيل');
        }
      } catch (error) {
        console.error('Error enrolling in course:', error);
        alert('حدث خطأ في التسجيل. يرجى المحاولة مرة أخرى.');
      }
    }

    // Add to wishlist
    function addToWishlist(courseId) {
      // In a real implementation, this would save to the backend
      let wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
      
      if (!wishlist.includes(courseId)) {
        wishlist.push(courseId);
        localStorage.setItem('wishlist', JSON.stringify(wishlist));
        alert('تم إضافة الدورة للمفضلة! ❤️');
      } else {
        alert('هذه الدورة موجودة بالفعل في المفضلة');
      }
    }

    // Redirect to login
    function redirectToLogin() {
      // Store current page URL to redirect back after login
      sessionStorage.setItem('redirectUrl', window.location.href);
      window.location.href = '/login.html';
    }

    // Show loading
    function showLoading() {
      const spinner = document.getElementById('loadingSpinner');
      if (spinner) spinner.style.display = 'flex';
    }

    // Hide loading
    function hideLoading() {
      const spinner = document.getElementById('loadingSpinner');
      if (spinner) spinner.style.display = 'none';
    }

    // Show error
    function showError(message) {
      alert(message);
      // Optionally redirect to courses page
      // window.location.href = '/courses.html';
    }

    // Mobile menu toggle
    document.getElementById('mobile-toggle')?.addEventListener('click', function() {
      const navLinks = document.querySelector('.nav-links');
      navLinks.classList.toggle('active');
    });

    // Handle redirect after login
    if (sessionStorage.getItem('redirectUrl') && localStorage.getItem('token')) {
      sessionStorage.removeItem('redirectUrl');
      location.reload();
    }

    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Add loading states for buttons
    function addLoadingState(button) {
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
      button.disabled = true;
      
      return function() {
        button.innerHTML = originalText;
        button.disabled = false;
      };
    }

    // Enhanced error handling
    window.addEventListener('unhandledrejection', function(event) {
      console.error('Unhandled promise rejection:', event.reason);
      showError('حدث خطأ غير متوقع. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
    });

    // Add intersection observer for animations
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      });

      // Observe elements with animation class
      document.querySelectorAll('.curriculum-item, .feature-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
      });
    }