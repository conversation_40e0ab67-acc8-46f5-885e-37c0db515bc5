<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار Sidebar الدورات - منصة دوراتي</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="/css/sidebar.css">
  <style>
    :root {
      --primary-color: #2563eb;
      --primary-dark: #1d4ed8;
      --primary-light: #3b82f6;
      --secondary-color: #10b981;
      --danger-color: #ef4444;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      --white: #ffffff;
      --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --font-size-sm: 0.875rem;
      --font-size-base: 1rem;
      --font-size-lg: 1.125rem;
      --font-size-xl: 1.25rem;
      --font-size-2xl: 1.5rem;
      --border-radius: 0.5rem;
      --border-radius-lg: 0.75rem;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --transition: all 0.3s ease;
    }

    body {
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-50);
      direction: rtl;
      margin: 0;
      padding: 0;
    }

    .navbar {
      background: var(--gray-900);
      box-shadow: var(--shadow-sm);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 1rem 0;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--white);
      text-decoration: none;
    }

    .desktop-nav {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 1.5rem;
      margin: 0;
      padding: 0;
    }

    .nav-link {
      color: var(--gray-300);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .nav-link:hover,
    .nav-link.active {
      color: var(--white);
      background-color: rgba(59, 130, 246, 0.1);
    }

    .nav-cta {
      display: flex;
      gap: 1rem;
    }

    .btn {
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      text-decoration: none;
      font-weight: 600;
      transition: var(--transition);
      border: none;
      cursor: pointer;
    }

    .btn-secondary {
      background: transparent;
      color: var(--gray-300);
      border: 1px solid var(--gray-600);
    }

    .btn-secondary:hover {
      background: var(--gray-700);
      color: var(--white);
    }

    .btn-primary {
      background: var(--primary-color);
      color: var(--white);
    }

    .btn-primary:hover {
      background: var(--primary-dark);
    }

    .main-content {
      margin-top: 80px;
      padding: 2rem;
      min-height: calc(100vh - 80px);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .test-section {
      background: var(--white);
      border-radius: var(--border-radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-sm);
    }

    .test-buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      margin-top: 1rem;
    }

    .test-btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      font-weight: 600;
      transition: var(--transition);
    }

    .test-btn.primary {
      background: var(--primary-color);
      color: var(--white);
    }

    .test-btn.secondary {
      background: var(--gray-200);
      color: var(--gray-800);
    }

    .test-btn.success {
      background: var(--secondary-color);
      color: var(--white);
    }

    .test-btn.danger {
      background: var(--danger-color);
      color: var(--white);
    }

    @media (max-width: 768px) {
      .desktop-nav {
        display: none !important;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <a href="/" class="navbar-brand">
        <i class="fas fa-graduation-cap"></i>
        منصة دوراتي
      </a>

      <!-- Mobile Menu Toggle -->
      <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>

      <!-- Desktop Navigation -->
      <div class="desktop-nav">
        <ul class="nav-links">
          <li><a href="/" class="nav-link">الرئيسية</a></li>
          <li><a href="/courses.html" class="nav-link active">الدورات</a></li>
          <li><a href="#about" class="nav-link">من نحن</a></li>
          <li><a href="#contact" class="nav-link">تواصل معنا</a></li>
        </ul>

        <div class="nav-cta">
          <a href="/login.html" class="btn btn-secondary">تسجيل الدخول</a>
          <a href="/register.html" class="btn btn-primary">ابدأ الآن</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Mobile Sidebar -->
  <div class="mobile-sidebar" id="mobileSidebar">
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    <div class="sidebar-content">
      <div class="sidebar-header">
        <div class="sidebar-brand">
          <i class="fas fa-graduation-cap"></i>
          منصة دوراتي
        </div>
        <button class="sidebar-close" id="sidebarClose">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <!-- User section for logged in users -->
      <div class="sidebar-user" id="sidebarUserSection" style="display: none;">
        <div class="sidebar-user-info">
          <img id="sidebarProfileImage" src="/uploads/no-photo.jpg" class="sidebar-profile-image" alt="الصورة الشخصية">
          <div class="sidebar-user-details">
            <span id="sidebarUserName">مرحباً</span>
            <small id="sidebarUserRole">مستخدم</small>
          </div>
        </div>
      </div>

      <div class="sidebar-menu">
        <a href="/" class="sidebar-link">
          <i class="fas fa-home"></i>
          <span>الرئيسية</span>
        </a>
        <a href="/courses.html" class="sidebar-link active">
          <i class="fas fa-book-open"></i>
          <span>الدورات</span>
        </a>
        <a href="#about" class="sidebar-link">
          <i class="fas fa-info-circle"></i>
          <span>من نحن</span>
        </a>
        <a href="#contact" class="sidebar-link">
          <i class="fas fa-envelope"></i>
          <span>تواصل معنا</span>
        </a>
        
        <!-- Auth links for non-logged users -->
        <div class="sidebar-auth" id="sidebarAuthSection">
          <a href="/login.html" class="sidebar-link">
            <i class="fas fa-sign-in-alt"></i>
            <span>تسجيل الدخول</span>
          </a>
          <a href="/register.html" class="sidebar-link">
            <i class="fas fa-user-plus"></i>
            <span>إنشاء حساب</span>
          </a>
        </div>
        
        <!-- User links for logged in users -->
        <div class="sidebar-user-links" id="sidebarUserLinks" style="display: none;">
          <a href="/profile.html" class="sidebar-link">
            <i class="fas fa-user"></i>
            <span>الملف الشخصي</span>
          </a>
          <a href="#" id="sidebarDashboardLink" class="sidebar-link">
            <i class="fas fa-tachometer-alt"></i>
            <span id="sidebarDashboardText">لوحة التحكم</span>
          </a>
          <button id="sidebarLogoutBtn" class="sidebar-link logout-link">
            <i class="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <div class="container">
      <div class="test-section">
        <h1>اختبار Sidebar صفحة الدورات</h1>
        <p>هذه صفحة اختبار للـ sidebar المخصص لصفحة الدورات. يتضمن روابط خاصة بالدورات وحالات مختلفة للمستخدمين.</p>
        
        <div class="test-buttons">
          <button class="test-btn primary" onclick="simulateLogin()">محاكاة تسجيل الدخول</button>
          <button class="test-btn danger" onclick="simulateLogout()">محاكاة تسجيل الخروج</button>
          <button class="test-btn secondary" onclick="window.sidebarUtils.openSidebar()">فتح Sidebar</button>
          <button class="test-btn secondary" onclick="window.sidebarUtils.closeSidebar()">إغلاق Sidebar</button>
        </div>
      </div>

      <div class="test-section">
        <h2>حالات المستخدم المختلفة</h2>
        <p>اختبر الـ sidebar مع أدوار مختلفة للمستخدمين:</p>
        
        <div class="test-buttons">
          <button class="test-btn success" onclick="simulateUserRole('student')">طالب</button>
          <button class="test-btn success" onclick="simulateUserRole('instructor')">مدرس</button>
          <button class="test-btn success" onclick="simulateUserRole('admin')">مدير</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="/js/sidebar.js"></script>
  <script>
    // Simulate login
    function simulateLogin() {
      const userData = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'student',
        profileImage: '/uploads/no-photo.jpg'
      };
      
      localStorage.setItem('token', 'fake-token');
      localStorage.setItem('userData', JSON.stringify(userData));
      
      if (typeof handleSidebarAuthState === 'function') {
        handleSidebarAuthState();
      }
      
      alert('تم محاكاة تسجيل الدخول!');
    }

    // Simulate logout
    function simulateLogout() {
      localStorage.removeItem('token');
      localStorage.removeItem('userData');
      
      if (typeof handleSidebarAuthState === 'function') {
        handleSidebarAuthState();
      }
      
      alert('تم محاكاة تسجيل الخروج!');
    }

    // Simulate different user roles
    function simulateUserRole(role) {
      const userData = {
        name: role === 'admin' ? 'سارة الإدارية' : role === 'instructor' ? 'محمد المدرس' : 'أحمد الطالب',
        email: `${role}@example.com`,
        role: role,
        profileImage: '/uploads/no-photo.jpg'
      };
      
      localStorage.setItem('token', 'fake-token');
      localStorage.setItem('userData', JSON.stringify(userData));
      
      if (typeof handleSidebarAuthState === 'function') {
        handleSidebarAuthState();
      }
      
      alert(`تم تغيير الدور إلى: ${role === 'admin' ? 'مدير' : role === 'instructor' ? 'مدرس' : 'طالب'}`);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Simulate courses page functionality
      if (typeof handleSidebarAuthState === 'function') {
        handleSidebarAuthState();
      }
    });
  </script>
</body>
</html>
