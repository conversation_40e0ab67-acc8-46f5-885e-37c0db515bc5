  /* متغيرات الألوان والخطوط */
    :root {
      --primary-color: #0f0f23;
      --primary-dark: #0a0a1a;
      --primary-light: #1a1a3a;
      --secondary-color: #6366f1;
      --accent-color: #f59e0b;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --info-color: #3b82f6;

      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;

      --white: #ffffff;
      --black: #000000;

      --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --font-size-xs: 0.7rem;
      --font-size-sm: 0.85rem;
      --font-size-base: 0.95rem;
      --font-size-lg: 1.1rem;
      --font-size-xl: 1.35rem;
      --font-size-2xl: 1.8rem;
      --font-size-3xl: 2.2rem;
      --font-size-4xl: 2.8rem;

      --border-radius: 0.8rem;
      --border-radius-lg: 1rem;
      --border-radius-xl: 1.2rem;

      --shadow-sm: 0 1px 4px 0 rgb(0 0 0 / 0.05);
      --shadow: 0 2px 8px 0 rgb(0 0 0 / 0.08), 0 1px 2px -1px rgb(0 0 0 / 0.06);
      --shadow-md: 0 4px 12px -2px rgb(0 0 0 / 0.10), 0 2px 4px -2px rgb(0 0 0 / 0.08);
      --shadow-lg: 0 8px 20px -4px rgb(0 0 0 / 0.12), 0 4px 8px -4px rgb(0 0 0 / 0.10);

      --transition: all 0.3s cubic-bezier(.4,0,.2,1);
    }

    /* الأساسيات */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--gray-800);
      background: linear-gradient(135deg, var(--gray-50) 0%, #fafbff 100%);
      min-height: 100vh;
      direction: rtl;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    /* شريط التنقل المحسن */
    .navbar {
      background: var(--primary-color);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 0.8rem 0;
      box-shadow: 0 4px 20px rgba(15, 15, 35, 0.15);
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      position: relative;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 800;
      color: var(--white);
      text-decoration: none;
      transition: var(--transition);
    }

    .navbar-brand:hover {
      color: var(--secondary-color);
      transform: scale(1.02);
    }

    .navbar-brand i {
      font-size: 1.5rem;
      color: var(--secondary-color);
    }

    /* Desktop Navigation */
    .desktop-nav {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 1.5rem;
      align-items: center;
    }

    .nav-link {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      font-weight: 600;
      padding: 0.6rem 1rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
      font-size: var(--font-size-base);
    }

    .nav-link:hover,
    .nav-link.active {
      color: var(--white);
      background: var(--secondary-color);
      transform: translateY(-2px);
    }

    .nav-cta {
      display: flex;
      gap: 0.8rem;
      align-items: center;
    }

    .user-menu {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-name {
      color: var(--secondary-color);
      font-weight: 700;
      font-size: var(--font-size-base);
    }

    /* Mobile Navigation */
    .mobile-toggle {
      display: none;
      flex-direction: column;
      cursor: pointer;
      gap: 0.25rem;
      padding: 0.5rem;
      border-radius: var(--border-radius);
      transition: var(--transition);
    }

    .mobile-toggle:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .mobile-toggle span {
      width: 25px;
      height: 3px;
      background: var(--white);
      border-radius: 2px;
      transition: var(--transition);
    }

    .mobile-toggle.active span:nth-child(1) {
      transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-toggle.active span:nth-child(2) {
      opacity: 0;
    }

    .mobile-toggle.active span:nth-child(3) {
      transform: rotate(-45deg) translate(6px, -6px);
    }

    /* Mobile Menu */
    .mobile-menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: var(--primary-color);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      transform: translateY(-100%);
      opacity: 0;
      visibility: hidden;
      transition: var(--transition);
      box-shadow: 0 8px 20px rgba(15, 15, 35, 0.3);
    }

    .mobile-menu.active {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav-links {
      list-style: none;
      padding: 1rem 0;
    }

    .mobile-nav-links li {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-nav-links li:last-child {
      border-bottom: none;
    }

    .mobile-nav-links .nav-link {
      display: block;
      padding: 1rem 1.5rem;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }

    .mobile-nav-links .nav-link:hover,
    .mobile-nav-links .nav-link.active {
      background: rgba(99, 102, 241, 0.2);
      color: var(--white);
      transform: none;
    }

    .mobile-auth {
      padding: 1rem 1.5rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
    }

    /* الأزرار */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.7rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      font-size: var(--font-size-sm);
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: var(--transition);
      white-space: nowrap;
    }

    .btn-primary {
      background: var(--secondary-color);
      color: var(--white);
      border: none;
    }

    .btn-primary:hover {
      background: #5855eb;
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.15);
      color: var(--white);
      backdrop-filter: blur(10px);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-2px);
    }

    .btn-outline {
      background: transparent;
      color: var(--white);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-outline:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: var(--white);
    }

    /* الهيرو */
    .hero-section {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
      padding: 7rem 0 4rem;
      color: var(--white);
      text-align: center;
      margin-top: 70px;
      position: relative;
      overflow: hidden;
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="800" cy="300" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="400" cy="600" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="900" cy="700" r="3" fill="rgba(255,255,255,0.1)"/></svg>');
      opacity: 0.6;
    }

    .hero-content {
      position: relative;
      z-index: 2;
    }

    .hero-content h1 {
      font-size: var(--font-size-3xl);
      font-weight: 800;
      margin-bottom: 1.5rem;
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .hero-content p {
      font-size: var(--font-size-lg);
      margin-bottom: 2.5rem;
      opacity: 0.95;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    /* فلاتر الكورسات */
    .courses-filters {
      max-width: 800px;
      margin: 0 auto;
    }

    .search-container {
      display: flex;
      margin-bottom: 2rem;
      background: rgba(255, 255, 255, 0.95);
      border-radius: var(--border-radius-xl);
      overflow: hidden;
      box-shadow: var(--shadow-lg);
      backdrop-filter: blur(10px);
    }

    .search-container input {
      flex: 1;
      padding: 1rem 1.25rem;
      border: none;
      font-size: var(--font-size-base);
      color: var(--gray-700);
      background: transparent;
    }

    .search-container input:focus {
      outline: none;
    }

    .search-container input::placeholder {
      color: var(--gray-400);
    }

    .search-btn {
      background: var(--secondary-color);
      color: var(--white);
      border: none;
      padding: 1rem 1.5rem;
      cursor: pointer;
      transition: var(--transition);
      font-size: var(--font-size-base);
    }

    .search-btn:hover {
      background: #5855eb;
    }

    .filter-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 0.8rem;
    }

    .filter-container select {
      padding: 0.8rem 1rem;
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius);
      background: rgba(255, 255, 255, 0.1);
      color: var(--white);
      font-size: var(--font-size-sm);
      backdrop-filter: blur(10px);
      cursor: pointer;
    }

    .filter-container select option {
      background: var(--gray-800);
      color: var(--white);
    }

    /* Courses Section */
    .courses-section {
      padding: 3rem 0;
      background: var(--gray-50);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .results-info {
      font-size: var(--font-size-base);
      color: var(--gray-600);
      font-weight: 600;
    }

    .sort-container select {
      padding: 0.6rem 0.8rem;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      background: var(--white);
      font-size: var(--font-size-sm);
      color: var(--gray-700);
      cursor: pointer;
    }

    /* Loading State */
    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem 0;
      color: var(--gray-500);
    }

    .loading-state .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--gray-200);
      border-top: 3px solid var(--secondary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Courses Grid - أصغر وأكثر */
    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1.2rem;
      margin-bottom: 2rem;
    }

    /* Course Card - أصغر */
    .course-card {
      background: var(--white);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
      box-shadow: var(--shadow-sm);
      transition: var(--transition);
      cursor: pointer;
      height: fit-content;
      border: 1px solid var(--gray-200);
    }

    .course-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-lg);
      border-color: var(--secondary-color);
    }

    .course-image {
      position: relative;
      height: 160px;
      overflow: hidden;
      background: var(--gray-100);
    }

    .course-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: var(--transition);
    }

    .course-card:hover .course-image img {
      transform: scale(1.03);
    }

    .course-level {
      position: absolute;
      top: 0.8rem;
      right: 0.8rem;
      background: rgba(15, 15, 35, 0.9);
      color: var(--white);
      padding: 0.3rem 0.6rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-xs);
      font-weight: 600;
      backdrop-filter: blur(10px);
    }

    .course-price {
      position: absolute;
      bottom: 0.8rem;
      right: 0.8rem;
      background: var(--secondary-color);
      color: var(--white);
      padding: 0.4rem 0.8rem;
      border-radius: var(--border-radius);
      font-weight: 700;
      font-size: var(--font-size-sm);
      box-shadow: var(--shadow-md);
    }

    .course-video-count {
      position: absolute;
      top: 0.8rem;
      left: 0.8rem;
      background: rgba(0, 0, 0, 0.7);
      color: var(--white);
      padding: 0.3rem 0.6rem;
      border-radius: var(--border-radius);
      font-size: var(--font-size-xs);
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }

    .course-content {
      padding: 1.2rem;
    }

    .course-category {
      color: var(--secondary-color);
      font-size: var(--font-size-xs);
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 0.5rem;
      display: inline-block;
      background: rgba(99, 102, 241, 0.1);
      padding: 0.2rem 0.5rem;
      border-radius: var(--border-radius);
    }

    .course-title {
      font-size: var(--font-size-lg);
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 0.8rem;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .course-description {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      margin-bottom: 1rem;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .course-instructor {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      margin-bottom: 1rem;
      font-weight: 500;
    }

    .course-instructor i {
      color: var(--secondary-color);
    }

    .course-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--font-size-xs);
      color: var(--gray-500);
      padding-top: 0.8rem;
      border-top: 1px solid var(--gray-200);
      gap: 0.5rem;
    }

    .course-rating,
    .course-students,
    .course-videos {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      font-weight: 500;
    }

    .course-rating i {
      color: var(--warning-color);
    }

    .course-students i,
    .course-videos i {
      color: var(--secondary-color);
    }

    .course-duration {
      display: block;
      color: var(--gray-400);
      font-size: var(--font-size-xs);
      margin-top: 0.2rem;
    }

    /* No Results */
    .no-results {
      text-align: center;
      padding: 4rem 0;
      color: var(--gray-500);
    }

    .no-results i {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: var(--gray-300);
    }

    .no-results h3 {
      font-size: var(--font-size-xl);
      margin-bottom: 0.5rem;
      color: var(--gray-600);
      font-weight: 600;
    }

    /* Pagination */
    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
    }

    .pagination-controls {
      display: flex;
      gap: 0.4rem;
      align-items: center;
    }

    .pagination-btn {
      padding: 0.8rem 1rem;
      border: 1px solid var(--gray-300);
      background: var(--white);
      color: var(--gray-700);
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition);
      font-weight: 600;
      min-width: 40px;
      font-size: var(--font-size-sm);
    }

    .pagination-btn:hover {
      background: var(--secondary-color);
      color: var(--white);
      border-color: var(--secondary-color);
    }

    .pagination-btn.active {
      background: var(--secondary-color);
      color: var(--white);
      border-color: var(--secondary-color);
    }

    /* Footer */
    .footer {
      background: var(--primary-color);
      color: var(--gray-300);
      padding: 3rem 0 1.5rem;
      margin-top: 3rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 2rem;
      margin-bottom: 1.5rem;
    }

    .footer-section h4 {
      color: var(--white);
      font-size: var(--font-size-lg);
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .footer-brand {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: var(--font-size-xl);
      font-weight: 700;
      color: var(--secondary-color);
      margin-bottom: 0.8rem;
    }

    .footer-section p {
      line-height: 1.6;
      margin-bottom: 0.8rem;
      font-size: var(--font-size-sm);
    }

    .footer-section ul {
      list-style: none;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: var(--gray-300);
      text-decoration: none;
      transition: var(--transition);
      font-size: var(--font-size-sm);
    }

    .footer-section ul li a:hover {
      color: var(--secondary-color);
    }

    .contact-info p {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: var(--font-size-sm);
    }

    .contact-info i {
      color: var(--secondary-color);
      width: 16px;
    }

    .social-links {
      display: flex;
      gap: 0.8rem;
      margin-top: 1rem;
    }

    .social-links a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background: var(--primary-light);
      color: var(--gray-300);
      border-radius: var(--border-radius);
      transition: var(--transition);
    }

    .social-links a:hover {
      background: var(--secondary-color);
      color: var(--white);
    }

    .footer-bottom {
      text-align: center;
      padding-top: 1.5rem;
      border-top: 1px solid var(--primary-light);
      color: var(--gray-400);
      font-size: var(--font-size-sm);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .courses-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1rem;
      }

      .hero-content h1 {
        font-size: var(--font-size-2xl);
      }
    }

    @media (max-width: 768px) {
      .desktop-nav {
        display: none;
      }

      .mobile-toggle {
        display: flex;
      }

      .hero-section {
        padding: 5rem 0 3rem;
      }

      .hero-content h1 {
        font-size: var(--font-size-xl);
      }

      .hero-content p {
        font-size: var(--font-size-base);
        margin-bottom: 2rem;
      }

      .filter-container {
        grid-template-columns: 1fr;
        gap: 0.6rem;
      }

      .section-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 0.8rem;
      }

      .courses-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 1rem;
      }

      .course-meta {
        flex-direction: column;
        gap: 0.8rem;
        align-items: flex-start;
      }

      .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
      }

      .social-links {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 0 0.8rem;
      }

      .hero-section {
        padding: 4rem 0 2.5rem;
      }

      .search-container {
        flex-direction: column;
      }

      .search-btn {
        border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
      }

      .courses-grid {
        grid-template-columns: 1fr;
      }

      .course-content {
        padding: 1rem;
      }

      .course-title {
        font-size: var(--font-size-base);
      }
    }

    /* Animation */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .course-card {
      animation: fadeIn 0.4s ease-out;
    }