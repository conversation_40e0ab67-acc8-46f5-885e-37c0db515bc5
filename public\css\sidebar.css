/* Mobile Sidebar Styles - Arabic RTL */

/* Import variables from profile.css if not already defined */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
  transition: all 0.3s ease;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background: var(--gray-100);
  margin: 3px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
}

.mobile-menu-toggle:hover .hamburger-line {
  background: var(--primary-light);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Sidebar */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-sidebar.active {
  visibility: visible;
  opacity: 1;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  cursor: pointer;
}

.sidebar-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  max-width: 85vw;
  height: 100%;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
  direction: rtl;
}

.mobile-sidebar.active .sidebar-content {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  background: rgba(30, 41, 59, 0.8);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-100);
}

.sidebar-brand i {
  color: var(--primary-light);
  font-size: 1.5rem;
}

.sidebar-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  color: var(--gray-300);
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-close:hover {
  background: rgba(59, 130, 246, 0.2);
  color: var(--primary-light);
  transform: scale(1.1);
}

/* Sidebar User */
.sidebar-user {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
}

.sidebar-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-profile-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-light);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.sidebar-user-details {
  flex: 1;
}

.sidebar-user-details span {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-100);
  margin-bottom: 0.25rem;
}

.sidebar-user-details small {
  color: var(--gray-400);
  font-size: 0.875rem;
}

/* Sidebar Menu */
.sidebar-menu {
  padding: 1rem 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  color: var(--gray-300);
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  font-size: 1rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.sidebar-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-light);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar-link:hover,
.sidebar-link.active {
  color: var(--gray-100);
  background: rgba(59, 130, 246, 0.1);
  text-decoration: none;
}

.sidebar-link:hover::before,
.sidebar-link.active::before {
  transform: scaleY(1);
}

.sidebar-link i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
  color: var(--primary-light);
}

.sidebar-link span {
  font-weight: 500;
}

.logout-link {
  color: var(--danger-color) !important;
  margin-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  padding-top: 2rem;
}

.logout-link:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: var(--danger-color) !important;
}

.logout-link i {
  color: var(--danger-color) !important;
}

/* Desktop Navigation - Hide on Mobile */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
  }
  
  .desktop-nav {
    display: none !important;
  }
}

/* Desktop - Hide Mobile Elements */
@media (min-width: 769px) {
  .mobile-menu-toggle,
  .mobile-sidebar {
    display: none !important;
  }
}

/* Animation for sidebar links */
.sidebar-link {
  transform: translateX(20px);
  opacity: 0;
  animation: slideInRight 0.3s ease forwards;
}

.sidebar-link:nth-child(1) { animation-delay: 0.1s; }
.sidebar-link:nth-child(2) { animation-delay: 0.2s; }
.sidebar-link:nth-child(3) { animation-delay: 0.3s; }
.sidebar-link:nth-child(4) { animation-delay: 0.4s; }
.sidebar-link:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInRight {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Smooth scrolling for sidebar */
.sidebar-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Additional Mobile Optimizations */
@media (max-width: 480px) {
  .sidebar-content {
    width: 100%;
    max-width: 100vw;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .sidebar-user {
    padding: 1.5rem 1rem;
  }

  .sidebar-profile-image {
    width: 50px;
    height: 50px;
  }

  .sidebar-link {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }
}

/* Accessibility improvements */
.mobile-menu-toggle:focus,
.sidebar-close:focus,
.sidebar-link:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

/* Prevent body scroll when sidebar is open */
body.sidebar-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Enhanced animations */
.sidebar-content {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-sidebar.active .sidebar-content {
  animation: slideInFromRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Ripple effect for sidebar links */
.sidebar-link {
  position: relative;
  overflow: hidden;
}

.sidebar-link::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.sidebar-link:active::after {
  width: 200px;
  height: 200px;
}
